#!/usr/bin/env python3
"""
测试脚本：验证Avalon和PubChem指纹的集成
"""

try:
    # 测试本地文件导入
    from metAppDomain_ADM import fpTypeDict, AVALON_AVAILABLE, PUBCHEM_AVAILABLE
    from rdkit import Chem
    
    print("=== 指纹集成测试 ===")
    print(f"可用的指纹类型: {list(fpTypeDict.keys())}")
    print(f"Avalon 可用: {AVALON_AVAILABLE}")
    print(f"PubChem 可用: {PUBCHEM_AVAILABLE}")
    
    # 测试分子
    test_smiles = "CC(=O)O"  # 醋酸
    mol = Chem.MolFromSmiles(test_smiles)
    
    print(f"\n使用测试分子: {test_smiles}")
    
    # 测试每种指纹类型
    successful_fps = []
    failed_fps = []
    
    for fp_type in fpTypeDict.keys():
        try:
            if fp_type == 'Morgan(bit)':
                fp = fpTypeDict[fp_type](mol, radius=2, nBits=1024)
            elif fp_type == 'Morgan(count)':
                fp = fpTypeDict[fp_type](mol, radius=2)
            elif fp_type.startswith('Avalon'):
                if AVALON_AVAILABLE:
                    fp = fpTypeDict[fp_type](mol, nBits=1024)
                else:
                    print(f"  ⚠️  {fp_type}: Avalon 不可用")
                    failed_fps.append(fp_type)
                    continue
            elif fp_type == 'PubChem':
                if PUBCHEM_AVAILABLE:
                    fp = fpTypeDict[fp_type](mol)
                else:
                    print(f"  ⚠️  {fp_type}: PubChem 不可用 (需要安装 scikit-fingerprints)")
                    failed_fps.append(fp_type)
                    continue
            else:
                fp = fpTypeDict[fp_type](mol)
            
            if fp is not None:
                print(f"  ✓  {fp_type}: 成功生成, 类型: {type(fp)}")
                successful_fps.append(fp_type)
            else:
                print(f"  ✗  {fp_type}: 返回 None")
                failed_fps.append(fp_type)
                
        except Exception as e:
            print(f"  ✗  {fp_type}: 错误 - {str(e)}")
            failed_fps.append(fp_type)
    
    print(f"\n=== 测试结果 ===")
    print(f"成功: {len(successful_fps)} 个指纹")
    print(f"失败: {len(failed_fps)} 个指纹")
    
    if successful_fps:
        print(f"成功的指纹: {', '.join(successful_fps)}")
    if failed_fps:
        print(f"失败的指纹: {', '.join(failed_fps)}")
    
    # 特别测试PubChem指纹的详细信息
    if 'PubChem' in successful_fps:
        try:
            pubchem_fp = fpTypeDict['PubChem'](mol)
            print(f"\nPubChem 指纹详情:")
            print(f"  - 位数: {pubchem_fp.GetNumBits()}")
            print(f"  - 开启的位数: {pubchem_fp.GetNumOnBits()}")
        except Exception as e:
            print(f"PubChem 详情获取失败: {e}")
    
    print("\n=== 测试完成 ===")

except ImportError as e:
    print(f"导入错误: {e}")
except Exception as e:
    print(f"测试过程中发生错误: {e}")
