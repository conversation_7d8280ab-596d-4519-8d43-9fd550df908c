import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

# 设置全局字体为Times New Roman
plt.rcParams['font.family'] = 'serif'
plt.rcParams['font.serif'] = 'Times New Roman'
plt.rcParams['mathtext.fontset'] = 'stix'  # 数学公式字体

# 原始参数保持不变
Task = 'AUC'  # 'n' , 'R2', 'RMSE', 'MAE, 'F1', 'AROC', 'RBA'
Wtmethod = 'exp'  # 'rigid', 'exp'

# 读取数据
file_name = f'model_{Wtmethod}_AD_{Task}.csv'
df = pd.read_csv(file_name, index_col=0)

# --------------------------
# 数据处理
# --------------------------
x = df.index.astype(float).values
y = df.columns.astype(float).values
z = df.values.T

# 创建更密集的网格用于平滑插值
from scipy.interpolate import griddata

# 原始数据点
X_orig, Y_orig = np.meshgrid(x, y)
points = np.column_stack((X_orig.flatten(), Y_orig.flatten()))
values = z.flatten()

# 创建更密集的插值网格
x_interp = np.linspace(x.min(), x.max(), 50)  # 大幅增加网格密度
y_interp = np.linspace(y.min(), y.max(), 50)  # 大幅增加网格密度
X, Y = np.meshgrid(x_interp, y_interp)

# 使用三次样条插值来平滑数据
z = griddata(points, values, (X, Y), method='cubic')

# 对插值结果进行额外的平滑处理
from scipy.ndimage import gaussian_filter
z_smooth = gaussian_filter(z, sigma=1.0)  # 高斯平滑
z = z_smooth

# --------------------------
# 绘制3D曲面图
# --------------------------
fig = plt.figure(figsize=(12, 8))
ax = fig.add_subplot(111, projection='3d')

surf = ax.plot_surface(
    X, Y, z,
    cmap='viridis',
    rcount=50,  # 进一步增加网格密度，使表面更细腻
    ccount=50,  # 进一步增加网格密度，使表面更细腻
    edgecolor='none',
    alpha=0.8,
    vmin=np.nanmin(z),  # 使用nanmin处理可能的NaN值
    vmax=np.nanmax(z),  # 使用nanmax处理可能的NaN值
    linewidth=0,
    antialiased=True,  # 启用抗锯齿
    shade=True  # 启用阴影，使表面更平滑
)

# 颜色条设置
cbar = fig.colorbar(surf, ax=ax, shrink=0.6, aspect=10)
# cbar.set_label(r'$n$', rotation=0, labelpad=20, fontsize=14)
cbar.set_label(r'$A_{\rm ROC}$', rotation=0, labelpad=20, fontsize=14)
# cbar.set_label(r'$R_{\rm BA}$', rotation=0, labelpad=20, fontsize=14)
# cbar.set_label(r'$R^{\mathrm{2}}$', rotation=0, labelpad=20, fontsize=14)
# cbar.set_label(r'$E_{\rm RMS}$', rotation=0, labelpad=20, fontsize=14)
cbar.ax.tick_params(labelsize=12)  # 颜色条刻度字体大小

# 轴标签设置
ax.set_xlabel('$I_{\mathrm{A,T}}$', fontsize=14, labelpad=12)
ax.set_ylabel('$ρ_{\mathrm{s,T}}$', fontsize=14, labelpad=12)

# 设置z轴标签为水平显示
# ax.set_zlabel(r'$n$', fontsize=14, labelpad=12)
ax.zaxis.set_rotate_label(False)  # 禁用自动旋转
ax.set_zlabel(r'$A_{\rm ROC}$', fontsize=14, labelpad=12, rotation=0)  # 强制水平旋转
# ax.set_zlabel(r'$R_{\rm BA}$', fontsize=14, labelpad=12, rotation=0)
# ax.set_zlabel(r'$R^{\mathrm{2}}$', fontsize=14, labelpad=12, rotation=0)
# ax.set_zlabel(r'$E_{\rm RMS}$', fontsize=14, labelpad=12, rotation=0)

# 刻度标签设置
ax.tick_params(axis='x', labelsize=12)
ax.tick_params(axis='y', labelsize=12)
ax.tick_params(axis='z', labelsize=12)

# 视角和标题
ax.view_init(elev=30, azim=225)
# plt.title(r'Number of compounds ($n$) retained in the AD ', fontsize=16, pad=20)
# plt.title(r'3D Surface Plot of $n$', fontsize=16, pad=20)
# plt.title(r'3D Surface Plot of $R_{\rm BA}$', fontsize=16, pad=20)


# 保存和显示
plt.savefig(f'model_{Wtmethod}_AD_{Task}.png', dpi=300, bbox_inches='tight')
plt.show()